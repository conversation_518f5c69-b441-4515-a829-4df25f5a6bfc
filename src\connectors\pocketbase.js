import PocketBase from 'pocketbase';
import { getImage } from 'astro:assets';

let POCKETBASE_URL = import.meta.env.POCKETBASE_URL;
const POCKETBASE_ADMIN_EMAIL = import.meta.env.POCKETBASE_ADMIN_EMAIL;
const POCKETBASE_ADMIN_PASSWORD = import.meta.env.POCKETBASE_ADMIN_PASSWORD;

// Clean up the PocketBase URL - remove admin panel paths
if (POCKETBASE_URL) {
    // Remove common admin panel paths that might be included by mistake
    POCKETBASE_URL = POCKETBASE_URL.replace(/\/_\/.*$/, '').replace(/\/admin.*$/, '');
    // Ensure it ends with a slash
    if (!POCKETBASE_URL.endsWith('/')) {
        POCKETBASE_URL += '/';
    }
}

// Debug environment variables
console.log('PocketBase Environment Variables:');
console.log('- POCKETBASE_URL (cleaned):', POCKETBASE_URL || 'Missing');
console.log('- POCKETBASE_ADMIN_EMAIL:', POCKETBASE_ADMIN_EMAIL ? 'Set' : 'Missing');
console.log('- POCKETBASE_ADMIN_PASSWORD:', POCKETBASE_ADMIN_PASSWORD ? 'Set' : 'Missing');

const TABLE_NAMES = [
    "social_agenda",
    "dancing_agenda",
    "reviews",
    "vocabulary",
    "bio",
    "services",
    "housing",
    "settings",
];

// Initialize PocketBase client
const pb = new PocketBase(POCKETBASE_URL);

// Disable auto-cancellation to prevent issues with multiple simultaneous requests
pb.autoCancellation(false);

// Cache for collection metadata and schemas to avoid repeated API calls
let collectionMetadataCache = null;
let collectionSchemasCache = new Map();

// Global authentication state to prevent multiple authentication calls
let authenticationPromise = null;

/**
 * Authenticate with PocketBase using admin credentials
 */
async function authenticateAdmin() {
    // Return existing authentication promise if one is in progress
    if (authenticationPromise) {
        console.log('🔐 Using existing authentication promise...');
        return authenticationPromise;
    }

    // Create new authentication promise
    authenticationPromise = (async () => {
        try {
            console.log('🔐 Attempting PocketBase admin authentication...');

            if (!POCKETBASE_URL || !POCKETBASE_ADMIN_EMAIL || !POCKETBASE_ADMIN_PASSWORD) {
                throw new Error('Missing required PocketBase environment variables');
            }

            if (!pb.authStore.isValid) {
                console.log('🔑 Authenticating with admin credentials...');

                // Try different authentication methods
                let authData = null;

                try {
                    // First try the deprecated but still working admins.authWithPassword
                    authData = await pb.admins.authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD);
                    console.log('✅ PocketBase admin authentication successful (using admins API)');
                } catch (adminError) {
                    console.log('⚠️ Admin API failed, trying collection-based auth...');
                    try {
                        // Try collection-based authentication for newer PocketBase versions
                        authData = await pb.collection('_superusers').authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD);
                        console.log('✅ PocketBase admin authentication successful (using collection API)');
                    } catch (collectionError) {
                        console.log('⚠️ Collection API failed, trying users collection...');
                        try {
                            // Try regular users collection as fallback
                            authData = await pb.collection('users').authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD);
                            console.log('✅ PocketBase authentication successful (using users collection)');
                        } catch (usersError) {
                            throw new Error(`All authentication methods failed. Admin: ${adminError.message}, Collection: ${collectionError.message}, Users: ${usersError.message}`);
                        }
                    }
                }

                console.log('🔍 Auth token:', authData?.token ? 'Present' : 'Missing');
                console.log('🔍 Auth record:', authData?.record ? 'Present' : 'Missing');
            } else {
                console.log('✅ Using existing valid authentication');
            }

            return true;
        } catch (error) {
            console.error('❌ PocketBase admin authentication failed:', error);
            console.error('🔍 Error details:', {
                message: error.message,
                status: error.status,
                data: error.data
            });
            // Reset the promise so it can be retried
            authenticationPromise = null;
            throw new Error(`Failed to authenticate with PocketBase: ${error.message}`);
        }
    })();

    return authenticationPromise;
}

/**
 * Fetch collection metadata from PocketBase
 */
async function fetchCollectionMetadata() {
    if (collectionMetadataCache) {
        console.log('📋 Using cached collection metadata');
        return collectionMetadataCache;
    }

    await authenticateAdmin();

    try {
        console.log('📋 Fetching collection metadata from PocketBase...');
        const collections = await pb.collections.getFullList();
        console.log(`📋 Found ${collections.length} collections in PocketBase`);

        // Debug: Log all collection names
        const collectionNames = collections.map(c => c.name);
        console.log('📋 Available collections:', collectionNames);

        // Check which required collections are missing
        const missingCollections = TABLE_NAMES.filter(name =>
            !collectionNames.some(cName => cName.toLowerCase() === name.toLowerCase())
        );

        if (missingCollections.length > 0) {
            console.warn('⚠️ Missing collections in PocketBase:', missingCollections);
        }

        collectionMetadataCache = collections;
        return collectionMetadataCache;
    } catch (error) {
        console.error('❌ Failed to fetch collection metadata:', error);
        console.error('🔍 Error details:', {
            message: error.message,
            status: error.status,
            data: error.data
        });
        throw new Error(`Failed to fetch collection metadata: ${error.message}`);
    }
}

/**
 * Fetch detailed schema for a specific collection
 */
async function fetchCollectionSchema(collectionId) {
    if (collectionSchemasCache.has(collectionId)) {
        return collectionSchemasCache.get(collectionId);
    }

    await authenticateAdmin();

    try {
        const collection = await pb.collections.getOne(collectionId);
        collectionSchemasCache.set(collectionId, collection);
        return collection;
    } catch (error) {
        console.error(`Failed to fetch collection schema for ${collectionId}:`, error);
        throw new Error(`Failed to fetch collection schema: ${error.message}`);
    }
}

/**
 * Extract field type information from collection schema
 */
function extractFieldTypesFromSchema(collection) {
    const fieldTypes = new Map();

    if (collection && collection.schema) {
        collection.schema.forEach(field => {
            // Skip system fields
            if (!field.system) {
                fieldTypes.set(field.name, {
                    type: field.type,
                    name: field.name,
                    options: field.options || null
                });
            }
        });
    }

    return fieldTypes;
}

/**
 * Get collection ID by name
 */
function getCollectionIdByName(collections, name) {
    const collection = collections.find(c => c.name?.toLowerCase() === name.toLowerCase());
    return collection?.id || null;
}

// ===== FIELD TRANSFORMER REGISTRY =====

/**
 * Registry for field transformers based on PocketBase field types
 */
class FieldTransformerRegistry {
    constructor() {
        this.transformers = new Map();
        this.initializeDefaultTransformers();
    }

    /**
     * Register a transformer for a specific field type
     */
    register(fieldType, transformer) {
        this.transformers.set(fieldType, transformer);
    }

    /**
     * Get transformer for a field type
     */
    getTransformer(fieldType) {
        return this.transformers.get(fieldType) || this.transformers.get('default');
    }

    /**
     * Transform a field value using the appropriate transformer
     */
    transform(fieldType, value, fieldInfo = null) {
        try {
            const transformer = this.getTransformer(fieldType);
            return transformer ? transformer(value, fieldInfo) : value;
        } catch (error) {
            console.warn(`Error in field transformer for type "${fieldType}":`, error);
            // Return original value if transformation fails
            return value;
        }
    }

    /**
     * Initialize default transformers for common PocketBase field types
     */
    initializeDefaultTransformers() {
        // File/attachment fields - Note: record context will be handled separately
        this.register('file', (value) => {
            // Don't transform here - will be handled in the special file handling section
            // that has access to the record context
            return value;
        });

        // Multi-select fields
        this.register('select', (value, fieldInfo) => {
            if (fieldInfo && fieldInfo.options && fieldInfo.options.maxSelect > 1) {
                // Multi-select
                if (value) {
                    return transformMultiSelect(value);
                }
                return [];
            } else {
                // Single select - return as-is
                return value;
            }
        });

        // Boolean fields
        this.register('bool', (value) => {
            if (typeof value === 'string') {
                return value.toLowerCase() === 'true' || value === '1';
            }
            return Boolean(value);
        });

        // Date/time fields
        this.register('date', (value) => transformDate(value));

        // Relation fields
        this.register('relation', (value) => transformRelation(value));

        // Numeric fields
        this.register('number', (value) => {
            if (value === null || value === undefined || value === '') return null;
            return parseFloat(value) || 0;
        });

        // Text fields - return as-is
        this.register('text', (value) => value);
        this.register('email', (value) => value);
        this.register('url', (value) => value);
        this.register('editor', (value) => value);

        // JSON fields
        this.register('json', (value) => {
            if (typeof value === 'string') {
                try {
                    return JSON.parse(value);
                } catch {
                    return value;
                }
            }
            return value;
        });

        // Default transformer for unknown types
        this.register('default', (value) => {
            // Fallback to dynamic detection for unknown field types
            // Note: File attachments will be handled in the special file handling section
            // that has access to the record context, so we don't transform them here
            if (containsMultiSelect(value)) {
                return transformMultiSelect(value);
            }
            return value;
        });
    }
}

// Create global transformer registry instance
const fieldTransformerRegistry = new FieldTransformerRegistry();

// ===== DATA TRANSFORMATION FUNCTIONS =====

/**
 * Transform PocketBase attachment to Airtable format
 */
function transformAttachment(pbAttachment, record) {
    if (!pbAttachment || typeof pbAttachment !== 'string' || pbAttachment.trim() === '') {
        // Don't warn for empty strings - this is normal for empty file fields
        return null;
    }

    // Validate record object
    if (!record || !record.id || !record.collectionId) {
        console.warn('Invalid record object for attachment:', {
            attachment: pbAttachment,
            record: record ? { id: record.id, collectionId: record.collectionId } : 'null'
        });
        return null;
    }

    // Validate PocketBase URL configuration
    if (!POCKETBASE_URL) {
        console.warn('POCKETBASE_URL not configured - cannot generate file URLs');
        return null;
    }

    try {
        // PocketBase stores file names as strings, we need to build the full URL
        // Use the newer getURL method with proper parameters
        let url;

        try {
            url = pb.files.getURL(record, pbAttachment);
        } catch (urlError) {
            console.warn('pb.files.getURL() failed, trying manual URL construction:', urlError.message);
            // Fallback: manually construct the URL
            // Format: {baseUrl}/api/files/{collectionId}/{recordId}/{filename}
            url = `${POCKETBASE_URL}api/files/${record.collectionId}/${record.id}/${pbAttachment}`;
        }

        if (!url) {
            console.warn('Attachment without valid URL:', {
                attachment: pbAttachment,
                recordId: record.id,
                collectionId: record.collectionId,
                pocketbaseUrl: POCKETBASE_URL
            });
            return null;
        }

        // Debug: Log successful URL generation (remove this in production)
        // console.log('✅ Generated file URL:', { attachment: pbAttachment, url: url });

        // Extract file information from filename
        const filename = pbAttachment;
        const lastDotIndex = filename.lastIndexOf('.');
        const type = lastDotIndex > 0 ? filename.substring(lastDotIndex + 1).toLowerCase() : '';

        // Determine MIME type based on extension
        const mimeTypeMap = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt': 'text/plain',
            'mp4': 'video/mp4',
            'mp3': 'audio/mpeg',
        };

        const mimeType = mimeTypeMap[type] || 'application/octet-stream';

        return {
            id: filename, // Use filename as ID
            url: url,
            filename: filename,
            size: 0, // PocketBase doesn't provide size in basic file info
            type: mimeType,
            thumbnails: {
                small: { url: url, width: 36, height: 36 },
                large: { url: url, width: 512, height: 512 },
                full: { url: url, width: 3000, height: 3000 }
            }
        };
    } catch (error) {
        console.warn('Error transforming attachment:', {
            attachment: pbAttachment,
            recordId: record?.id,
            collectionId: record?.collectionId,
            error: error.message || error,
            pocketbaseUrl: POCKETBASE_URL
        });
        return null;
    }
}

/**
 * Transform array of PocketBase attachments to Airtable format
 */
function transformAttachments(value, record) {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
        // Empty file fields are normal - don't log this
        return [];
    }

    // console.log('📎 Transforming attachments:', { value, type: typeof value, isArray: Array.isArray(value) });

    if (Array.isArray(value)) {
        const results = value.map(attachment => transformAttachment(attachment, record)).filter(Boolean);
        // console.log(`📎 Processed ${value.length} attachments, ${results.length} successful`);
        return results;
    }

    if (typeof value === 'string') {
        // Single attachment
        const attachment = transformAttachment(value, record);
        // console.log(`📎 Single attachment result:`, attachment ? 'success' : 'failed');
        return attachment ? [attachment] : [];
    }

    console.log('📎 Unknown attachment value type, returning empty array');
    return [];
}

/**
 * Check if a value contains attachments (dynamic detection)
 */
function containsAttachments(value) {
    if (!value) return false;

    if (Array.isArray(value)) {
        return value.some(item =>
            typeof item === 'string' &&
            item.trim() !== '' &&
            /\.(jpg|jpeg|png|gif|webp|pdf|doc|docx|txt|mp4|mp3)$/i.test(item)
        );
    }

    if (typeof value === 'string') {
        return value.trim() !== '' && /\.(jpg|jpeg|png|gif|webp|pdf|doc|docx|txt|mp4|mp3)$/i.test(value);
    }

    return false;
}

/**
 * Transform multi-select values
 */
function transformMultiSelect(value) {
    if (!value) return [];

    if (Array.isArray(value)) {
        return value;
    }

    if (typeof value === 'string') {
        // Handle comma-separated values
        return value.split(',').map(item => item.trim()).filter(Boolean);
    }

    return [];
}

/**
 * Check if a value contains multi-select data (dynamic detection)
 */
function containsMultiSelect(value) {
    if (!value) return false;

    if (Array.isArray(value)) {
        return value.length > 0 && value.every(item => typeof item === 'string');
    }

    if (typeof value === 'string') {
        // Check if it looks like comma-separated values
        return value.includes(',') && value.split(',').length > 1;
    }

    return false;
}

/**
 * Transform date values to ISO format
 */
function transformDate(value) {
    if (!value) return null;

    try {
        const date = new Date(value);
        if (isNaN(date.getTime())) return null;
        return date.toISOString();
    } catch (error) {
        console.warn('Error transforming date:', value, error);
        return null;
    }
}

/**
 * Transform relation values to Airtable format
 */
function transformRelation(value) {
    if (!value) return [];

    if (Array.isArray(value)) {
        return value.map(id => `rec${id}`);
    }

    if (typeof value === 'string') {
        return [`rec${value}`];
    }

    return [];
}

/**
 * Transform PocketBase record to Airtable format
 */
function transformRecord(pbRecord, collectionFieldTypes = null) {
    if (!pbRecord) {
        console.warn('🔄 Null record passed to transformRecord');
        return null;
    }

    const transformedFields = {};

    // Transform each field based on its type
    Object.entries(pbRecord).forEach(([fieldName, value]) => {
        // Skip system fields
        if (['id', 'created', 'updated', 'collectionId', 'collectionName'].includes(fieldName)) {
            return;
        }

        let transformedValue = value;

        // Use schema-based transformation if available
        if (collectionFieldTypes && collectionFieldTypes.has(fieldName)) {
            const fieldInfo = collectionFieldTypes.get(fieldName);
            transformedValue = fieldTransformerRegistry.transform(fieldInfo.type, value, fieldInfo);
        } else {
            // Fallback to dynamic detection
            transformedValue = fieldTransformerRegistry.transform('default', value);
        }

        // Special handling for file fields that need the record context
        const isFileField = (collectionFieldTypes && collectionFieldTypes.has(fieldName) &&
            collectionFieldTypes.get(fieldName).type === 'file') ||
            containsAttachments(value);

        if (isFileField && value) {
            // console.log(`🔗 Processing file field "${fieldName}":`, { value, recordId: pbRecord.id });
            transformedValue = transformAttachments(value, pbRecord);
        }

        transformedFields[fieldName] = transformedValue;
    });

    // Create Airtable-compatible record structure
    const airtableRecord = {
        id: `rec${pbRecord.id}`,
        fields: transformedFields,
        get: function (fieldName) {
            return this.fields[fieldName];
        }
    };

    return airtableRecord;
}

// ===== CORE FUNCTIONALITY =====

/**
 * Merge events function (same as Airtable connector)
 */
function mergeEvents(record) {
    const otherEvents = record.get('other_events') || [];
    const fixedEvents = record.get('fixed_events') || [];
    return [...otherEvents, ...fixedEvents];
}

// Note: OG image creation, update, and bootstrap functions are intentionally
// not implemented as per requirements

/**
 * Fetch data from a specific collection with pagination
 */
async function fetchDataFromCollection(collectionName) {
    const collectionNameSanitized = collectionName.toLowerCase();
    console.log(`\n🔍 Fetching data from collection: ${collectionName}`);

    if (!TABLE_NAMES.includes(collectionNameSanitized)) {
        console.warn(`⚠️ Collection "${collectionName}" not in TABLE_NAMES list`);
        return null;
    }

    try {
        await authenticateAdmin();

        const collections = await fetchCollectionMetadata();
        const collectionId = getCollectionIdByName(collections, collectionName);

        if (!collectionId) {
            console.warn(`❌ Collection with name "${collectionName}" not found in PocketBase`);
            return { table: collectionNameSanitized, data: [] };
        }

        console.log(`✅ Found collection "${collectionName}" with ID: ${collectionId}`);

        // Fetch collection schema for field type information
        let collectionFieldTypes = null;
        try {
            const collection = await fetchCollectionSchema(collectionId);
            collectionFieldTypes = extractFieldTypesFromSchema(collection);
            console.log(`📋 Schema loaded for ${collectionName}, found ${collectionFieldTypes.size} fields`);
        } catch (schemaError) {
            console.warn(`⚠️ Failed to fetch schema for collection ${collectionName}, falling back to dynamic detection:`, schemaError);
        }

        // Fetch ALL records using pagination
        let allRecords = [];
        let page = 1;
        const perPage = 100; // Fetch 100 records per request for efficiency
        let hasMoreRecords = true;

        console.log(`📄 Starting pagination for ${collectionName}...`);

        while (hasMoreRecords) {
            try {
                console.log(`📄 Fetching page ${page} from ${collectionName}...`);
                const records = await pb.collection(collectionName).getList(page, perPage);

                console.log(`📄 Page ${page} response:`, {
                    totalItems: records.totalItems,
                    totalPages: records.totalPages,
                    currentPage: records.page,
                    itemsInPage: records.items?.length || 0
                });

                if (records && records.items && records.items.length > 0) {
                    allRecords = allRecords.concat(records.items);
                    page++;
                    hasMoreRecords = records.items.length === perPage && page <= records.totalPages;
                    console.log(`📄 Added ${records.items.length} records, total so far: ${allRecords.length}`);
                } else {
                    console.log(`📄 No more records found, stopping pagination`);
                    hasMoreRecords = false;
                }
            } catch (error) {
                console.error(`❌ Error fetching page ${page} from collection ${collectionName}:`, error);
                console.error('🔍 Error details:', {
                    message: error.message,
                    status: error.status,
                    data: error.data
                });
                hasMoreRecords = false;
            }
        }

        console.log(`✅ Fetched ${allRecords.length} total records from collection ${collectionName}`);

        // Debug: Log first record structure if available
        if (allRecords.length > 0) {
            console.log(`🔍 Sample record structure:`, Object.keys(allRecords[0]));
        }

        // Transform PocketBase records to match Airtable structure using schema information
        const transformStart = Date.now();
        const transformedRecords = allRecords.map(record => transformRecord(record, collectionFieldTypes)).filter(Boolean);
        const transformTime = Date.now() - transformStart;

        if (collectionFieldTypes) {
            console.log(`🔄 Transformed ${transformedRecords.length} records using schema-based detection in ${transformTime}ms`);
        } else {
            console.log(`🔄 Transformed ${transformedRecords.length} records using dynamic detection in ${transformTime}ms`);
        }

        // Debug: Log first transformed record if available
        if (transformedRecords.length > 0) {
            console.log(`🔍 Sample transformed record:`, {
                id: transformedRecords[0].id,
                fieldsKeys: Object.keys(transformedRecords[0].fields || {}),
                hasGetMethod: typeof transformedRecords[0].get === 'function'
            });
        }

        return { table: collectionNameSanitized, data: transformedRecords };
    } catch (error) {
        console.error(`❌ Error fetching data from collection ${collectionName}:`, error);
        console.error('🔍 Error details:', {
            message: error.message,
            status: error.status,
            data: error.data
        });
        return { table: collectionNameSanitized, data: [] };
    }
}

/**
 * Fetch all configured collections from PocketBase
 */
async function fetchTablesFromPocketBase() {
    return Promise.all(TABLE_NAMES.map(fetchDataFromCollection));
}

/**
 * Normalize record function (same as other connectors)
 */
const normalizeRecord = (record) => {
    // Record is already transformed to Airtable format with id, fields, and get() method
    const mergedEvents = mergeEvents(record);
    const normalized = {
        id: record.id,
        ...record.fields,
        other_events: mergedEvents  // Replace other_events with merged events
    };

    return normalized;
};

// ===== INITIALIZATION AND EXPORTS =====

// Initialize data fetching and exports
let initPromise;

if (typeof window === 'undefined') {
    // Server-side initialization
    console.log('🚀 Initializing PocketBase connector (server-side)...');
    initPromise = (async () => {
        try {
            // Skip OG image generation as per requirements
            if (process.env.NODE_ENV === 'production') {
                console.log(`${process.env.NODE_ENV} Environment - OG image generation skipped for PocketBase connector`);
            }

            console.log('📊 Fetching all tables from PocketBase...');
            const tables = await fetchTablesFromPocketBase();
            console.log(`📊 Fetched ${tables.length} tables:`, tables.map(t => `${t.table} (${t.data.length} records)`));

            const getTable = (tableName) => {
                const table = tables.find((t) => t.table === tableName);
                if (!table) {
                    console.warn(`⚠️ Table "${tableName}" not found in fetched tables`);
                    return [];
                }
                console.log(`📋 Getting table "${tableName}": ${table.data.length} records`);
                return table.data;
            };

            console.log('🔄 Processing services...');
            const servicesRaw = getTable('services').map(normalizeRecord).filter(s => s.published);
            console.log(`🔄 Found ${servicesRaw.length} published services`);

            const processedServices = await Promise.all(servicesRaw.map(async (s) => {
                try {
                    if (s.covers && s.covers[0]?.url) {
                        return {
                            ...s,
                            cover: await getImage({ src: s.covers[0].url, format: 'webp', width: 1080, height: 1080 })
                        };
                    }
                    return s;
                } catch (error) {
                    console.warn('Error processing service image:', error);
                    return s;
                }
            }));

            const result = {
                latinEvents: getTable('dancing_agenda').map(normalizeRecord),
                socialEvents: getTable('social_agenda').map(normalizeRecord),
                reviews: getTable('reviews').map(normalizeRecord),
                vocabulary: getTable('vocabulary').map(normalizeRecord),
                bio: getTable('bio').map(normalizeRecord),
                settings: getTable('settings').map(normalizeRecord),
                properties: getTable('housing').map(normalizeRecord),
                services: processedServices
            };

            console.log('✅ PocketBase connector initialization complete!');
            console.log('📊 Final data summary:', {
                latinEvents: result.latinEvents.length,
                socialEvents: result.socialEvents.length,
                reviews: result.reviews.length,
                vocabulary: result.vocabulary.length,
                bio: result.bio.length,
                settings: result.settings.length,
                properties: result.properties.length,
                services: result.services.length
            });

            return result;
        } catch (error) {
            console.error('❌ Error initializing PocketBase connector:', error);
            console.error('🔍 Error details:', {
                message: error.message,
                stack: error.stack
            });
            // Return empty data structures to maintain compatibility
            return {
                latinEvents: [],
                socialEvents: [],
                reviews: [],
                vocabulary: [],
                bio: [],
                settings: [],
                properties: [],
                services: []
            };
        }
    })();
} else {
    // Client-side - return empty data
    console.log('🚀 Initializing PocketBase connector (client-side) - returning empty data');
    initPromise = Promise.resolve({
        latinEvents: [],
        socialEvents: [],
        reviews: [],
        vocabulary: [],
        bio: [],
        settings: [],
        properties: [],
        services: []
    });
}

// Export the data (same interface as other connectors)
const data = await initPromise;

export const latinEvents = data.latinEvents;
export const socialEvents = data.socialEvents;
export const reviews = data.reviews;
export const vocabulary = data.vocabulary;
export const bio = data.bio;
export const settings = data.settings;
export const properties = data.properties;
export const services = data.services;

// Export the fetch function for compatibility
export { fetchTablesFromPocketBase };
